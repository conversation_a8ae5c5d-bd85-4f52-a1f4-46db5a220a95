package com.ows.ufa.common.core.utils;

import java.lang.management.ManagementFactory;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;

import org.apache.commons.lang3.time.DateFormatUtils;

/**
 * 时间工具类
 *
 * <AUTHOR>
 */
public class DateUtils extends org.apache.commons.lang3.time.DateUtils
{
    public static String YYYY = "yyyy";

    public static String YYYY_MM = "yyyy-MM";

    public static String YYYY_MM_DD = "yyyy-MM-dd";

    public static String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

    public static String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    private static String[] parsePatterns = {
            "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
            "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
            "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM"};

    /**
     * 获取当前Date型日期
     *
     * @return Date() 当前日期
     */
    public static Date getNowDate()
    {
        return new Date();
    }

    /**
     * 获取当前日期, 默认格式为yyyy-MM-dd
     *
     * @return String
     */
    public static String getDate()
    {
        return dateTimeNow(YYYY_MM_DD);
    }

    public static final String getTime()
    {
        return dateTimeNow(YYYY_MM_DD_HH_MM_SS);
    }

    public static final String dateTimeNow()
    {
        return dateTimeNow(YYYYMMDDHHMMSS);
    }

    public static final String dateTimeNow(final String format)
    {
        return parseDateToStr(format, new Date());
    }

    public static final String dateTime(final Date date)
    {
        return parseDateToStr(YYYY_MM_DD, date);
    }

    public static final String parseDateToStr(final String format, final Date date)
    {
        return new SimpleDateFormat(format).format(date);
    }

    public static final Date dateTime(final String format, final String ts)
    {
        try
        {
            return new SimpleDateFormat(format).parse(ts);
        }
        catch (ParseException e)
        {
            throw new RuntimeException(e);
        }
    }

    /**
     * 日期路径 即年/月/日 如2018/08/08
     */
    public static final String datePath()
    {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyy/MM/dd");
    }

    /**
     * 日期路径 即年/月/日 如20180808
     */
    public static final String dateTime()
    {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyyMMdd");
    }

    /**
     * 日期型字符串转化为日期 格式
     */
    public static Date parseDate(Object str)
    {
        if (str == null)
        {
            return null;
        }
        try
        {
            return parseDate(str.toString(), parsePatterns);
        }
        catch (ParseException e)
        {
            return null;
        }
    }

    /**
     * 获取服务器启动时间
     */
    public static Date getServerStartDate()
    {
        long time = ManagementFactory.getRuntimeMXBean().getStartTime();
        return new Date(time);
    }

    /**
     * 计算时间差
     *
     * @param endDate 最后时间
     * @param startTime 开始时间
     * @return 时间差（天/小时/分钟）
     */
    public static String timeDistance(Date endDate, Date startTime)
    {
        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        // long ns = 1000;
        // 获得两个时间的毫秒时间差异
        long diff = endDate.getTime() - startTime.getTime();
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff % nd / nh;
        // 计算差多少分钟
        long min = diff % nd % nh / nm;
        // 计算差多少秒//输出结果
        // long sec = diff % nd % nh % nm / ns;
        return day + "天" + hour + "小时" + min + "分钟";
    }

    /**
     * 增加 LocalDateTime ==> Date
     */
    public static Date toDate(LocalDateTime temporalAccessor)
    {
        ZonedDateTime zdt = temporalAccessor.atZone(ZoneId.systemDefault());
        return Date.from(zdt.toInstant());
    }

    /**
     * 增加 LocalDate ==> Date
     */
    public static Date toDate(LocalDate temporalAccessor)
    {
        LocalDateTime localDateTime = LocalDateTime.of(temporalAccessor, LocalTime.of(0, 0, 0));
        ZonedDateTime zdt = localDateTime.atZone(ZoneId.systemDefault());
        return Date.from(zdt.toInstant());
    }

    /**
     * 获取近七天的起止时间（包含今天）
     *
     * @return
     */
    public static List<Map<String,Date>> getSevenDaysDates(){
        List<Map<String,Date>> startEndDates = new ArrayList<>();
        // 创建一个Calendar实例，设置为当天
        Calendar today = Calendar.getInstance();

        // 获取近七天的起止时间（包含今天）
        for (int i = 6; i >= 0; i--) {
            Map<String,Date> startEndDate = new HashMap<>();
            // 复制今天的日期，以便不修改原始today对象
            Calendar day = (Calendar) today.clone();
            // 将日期设置为当前循环的天数之前（i=0表示今天，i=1表示昨天，等等）
            day.add(Calendar.DATE, -i);

            // 获取当天的开始时间（即午夜12点）
            day.set(Calendar.HOUR_OF_DAY, 0);
            day.set(Calendar.MINUTE, 0);
            day.set(Calendar.SECOND, 0);
            day.set(Calendar.MILLISECOND, 0);

            // 获取当天的结束时间（即第二天的午夜12点前一秒）
            Calendar endOfDay = (Calendar) day.clone();
            endOfDay.add(Calendar.DATE, 1); // 设置为下一天
            endOfDay.add(Calendar.MILLISECOND, -1); // 减去1毫秒，得到当天的最后时刻

            startEndDate.put("startDate",day.getTime());
            startEndDate.put("endDate",endOfDay.getTime());
            startEndDates.add(startEndDate);
        }

        return startEndDates;
    }

    /**
     * 获取近一年每月起止时间
     *
     */
    public static List<Map<String,Date>> getYearMonthDates(){
        List<Map<String,Date>> startEndDates = new ArrayList<>();

        Calendar calendar = Calendar.getInstance();

        for (int i = 0; i < 12; i++) {
            Map<String,Date> startEndDate = new HashMap<>();

            // 定位到上个月最后一天的23:59:59
            calendar.add(Calendar.MONTH, -1);
            calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            // 月末时间（23:59:59）
            Calendar monthEnd = (Calendar) calendar.clone();

            // 月初时间（00:00:00）
            Calendar monthStart = (Calendar) calendar.clone();
            monthStart.set(Calendar.DAY_OF_MONTH, 1);
            monthStart.set(Calendar.HOUR_OF_DAY, 0);
            monthStart.set(Calendar.MINUTE, 0);
            monthStart.set(Calendar.SECOND, 0);

            startEndDate.put("startDate",monthStart.getTime());
            startEndDate.put("endDate",monthEnd.getTime());
            startEndDates.add(startEndDate);
        }

        Collections.reverse(startEndDates);
        return startEndDates;
    }

    /**
     * 获取当月起止时间
     *
     * @return
     */
    public static List<Map<String,Date>> getCurrentMonthDates(){
        List<Map<String,Date>> startEndDates = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();

        Map<String,Date> startEndDate = new HashMap<>();

        // 获取当月第一天的开始时间
        Calendar monthStart = (Calendar) calendar.clone();
        monthStart.set(Calendar.DAY_OF_MONTH, 1);
        monthStart.set(Calendar.HOUR_OF_DAY, 0);
        monthStart.set(Calendar.MINUTE, 0);
        monthStart.set(Calendar.SECOND, 0);
        monthStart.set(Calendar.MILLISECOND, 0);

        // 获取当月最后一天的结束时间
        Calendar monthEnd = (Calendar) calendar.clone();
        monthEnd.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        monthEnd.set(Calendar.HOUR_OF_DAY, 23);
        monthEnd.set(Calendar.MINUTE, 59);
        monthEnd.set(Calendar.SECOND, 59);
        monthEnd.set(Calendar.MILLISECOND, 999);

        startEndDate.put("startDate", monthStart.getTime());
        startEndDate.put("endDate", monthEnd.getTime());
        startEndDates.add(startEndDate);

        return startEndDates;
    }

    /**
     * 获取当年起止时间
     *
     * @return
     */
    public static List<Map<String,Date>> getCurrentYearDates(){
        List<Map<String,Date>> startEndDates = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();

        Map<String,Date> startEndDate = new HashMap<>();

        // 获取当年第一天的开始时间
        Calendar yearStart = (Calendar) calendar.clone();
        yearStart.set(Calendar.MONTH, Calendar.JANUARY);
        yearStart.set(Calendar.DAY_OF_MONTH, 1);
        yearStart.set(Calendar.HOUR_OF_DAY, 0);
        yearStart.set(Calendar.MINUTE, 0);
        yearStart.set(Calendar.SECOND, 0);
        yearStart.set(Calendar.MILLISECOND, 0);

        // 获取当年最后一天的结束时间
        Calendar yearEnd = (Calendar) calendar.clone();
        yearEnd.set(Calendar.MONTH, Calendar.DECEMBER);
        yearEnd.set(Calendar.DAY_OF_MONTH, 31);
        yearEnd.set(Calendar.HOUR_OF_DAY, 23);
        yearEnd.set(Calendar.MINUTE, 59);
        yearEnd.set(Calendar.SECOND, 59);
        yearEnd.set(Calendar.MILLISECOND, 999);

        startEndDate.put("startDate", yearStart.getTime());
        startEndDate.put("endDate", yearEnd.getTime());
        startEndDates.add(startEndDate);

        return startEndDates;
    }

    /**
     * 获取近五年每年起止时间
     *
     */
    public static List<Map<String,Date>> getFiveYearDates(){
        List<Map<String,Date>> startEndDates = new ArrayList<>();

        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        try {
            for (int i = 0; i < 5; i++) {
                Map<String, Date> startEndDate = new HashMap<>();
                calendar.add(Calendar.YEAR, -1);
                String startDateStr = calendar.get(Calendar.YEAR) + "-01-01 00:00:00";
                String endDateStr = calendar.get(Calendar.YEAR) + "-12-31 23:59:59";
                System.out.println(startDateStr + "              "+endDateStr);
                startEndDate.put("startDate", sdf.parse(startDateStr));
                startEndDate.put("endDate", sdf.parse(endDateStr));
                startEndDates.add(startEndDate);
            }
        }catch (Exception e){
            return null;
        }
        Collections.reverse(startEndDates);
        return startEndDates;
    }
}
