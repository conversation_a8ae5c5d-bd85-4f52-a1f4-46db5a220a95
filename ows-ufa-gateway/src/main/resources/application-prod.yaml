# spring配置
spring:
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: nacos:8848
      config:
        # 配置中心地址
        server-addr: nacos:8848
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
    gateway:
      discovery:
        locator:
          lowerCaseServiceId: true
          enabled: true
      routes:
        # 认证中心
        - id: ows-ufa-auth-additional
          uri: lb://ows-ufa-auth
          predicates:
            - Path=/owsz/auth/**
          filters:
            # 验证码处理
            - CacheRequestFilter
            - ValidateCodeFilter
            - StripPrefix=2
        - id: ows-ufa-auth
          uri: lb://ows-ufa-auth
          predicates:
            - Path=/auth/**
          filters:
            # 验证码处理
            - CacheRequestFilter
            - ValidateCodeFilter
            - StripPrefix=1
        # 代码生成
        - id: ows-ufa-gen
          uri: lb://ows-ufa-gen
          predicates:
            - Path=/code/**
          filters:
            - StripPrefix=1
        # 定时任务
        - id: ows-ufa-job
          uri: lb://ows-ufa-job
          predicates:
            - Path=/schedule/**
          filters:
            - StripPrefix=1
        # 系统模块
        - id: ows-ufa-system-additional
          uri: lb://ows-ufa-system
          predicates:
            - Path=/owsz/system/**
          filters:
            - StripPrefix=2
        - id: ows-ufa-system
          uri: lb://ows-ufa-system
          predicates:
            - Path=/system/**
          filters:
            - StripPrefix=1
        # 文件服务
        - id: ows-ufa-file
          uri: lb://ows-ufa-file
          predicates:
            - Path=/file/**
          filters:
            - StripPrefix=1
  redis:
    host: *************
    port: 9000
    password: Lmcc_58120427
    database: 11

# 安全配置
security:
  # 验证码
  captcha:
    enabled: true
    type: math
  # 防止XSS攻击
  xss:
    enabled: true
    excludeUrls:
      - /system/notice

  # 不校验白名单
  ignore:
    whites:
      - /auth/logout
      - /auth/login
      - /auth/thirdPartLogin
      - /auth/loginByUserId
      - /auth/register
      - /auth/loginNew
      - /owsz/auth/logout
      - /owsz/auth/login
      - /owsz/auth/thirdPartLogin
      - /owsz/auth/loginByUserId
      - /owsz/auth/register
      - /*/v2/api-docs
      - /*/v3/api-docs
      - /*/swagger-ui/**
      - /csrf
      - /system/admissionNotice/**
      - /system/open/**
      - /system/student/**
      - /owsz/system/admissionNotice/**
      - /owsz/system/open/**
      - /owsz/system/student/**
      - /owsz/system/questionnaireSubmit/touristSubmit
      - /system/h5/dataOverview/*
      - /owsz/system/h5/dataOverview/*
      - /system/datav/init/getDistrictYkzUidByToken
      - /owsz/system/datav/init/getDistrictYkzUidByToken
      - /system/datav/init/verifyToken
      - /owsz/system/datav/init/verifyToken
      - /system/h5/login/**
      - /system/dict/data/type2/*
# springdoc配置
springdoc:
  webjars:
    # 访问前缀
    prefix:
