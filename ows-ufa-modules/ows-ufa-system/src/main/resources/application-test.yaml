# spring配置
spring:
  cloud:
    nacos:
      enabled: true
      discovery:
        # 服务注册地址
        server-addr: nacos:8848
        enabled: true
      config:
        # 配置中心地址
        server-addr: nacos:8848
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
        enabled: true
  redis:
    host: **************
    port: 9000
    password: Jnsk_81908834
    database: 6
  datasource:
    druid:
      stat-view-servlet:
        enabled: true
        loginUsername: admin
        loginPassword: 123456
    dynamic:
      druid:
        initial-size: 5
        min-idle: 5
        maxActive: 20
        maxWait: 60000
        connectTimeout: 30000
        socketTimeout: 60000
        timeBetweenEvictionRunsMillis: 60000
        minEvictableIdleTimeMillis: 300000
        maxEvictableIdleTimeMillis: 900000
        keepAlive: true
        validationQuery: SELECT 1
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
        filters: stat,slf4j
        connectionProperties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
      datasource:
          # 主库数据源
          master:
            driver-class-name: com.huawei.gaussdb.jdbc.Driver
            url: *****************************************************************
            username: root
            password: H20Nvvna@Huawei
          # 从库数据源
          slave:
            enabled : true
            driver-class-name: com.huawei.gaussdb.jdbc.Driver
            url: *********************************************************************************************
            username: root
            password: H20Nvvna@Huawei

# mybatis配置
mybatis:
    # 搜索指定包别名
    typeAliasesPackage: com.ows.ufa.system
    # 配置mapper的扫描，找到所有的mapper.xml映射文件
    mapperLocations: classpath:mapper/**/*.xml
pagehelper:
  helperDialect: postgresql

# springdoc配置
springdoc:
  gatewayUrl: http://localhost:8080/NULL
  api-docs:
    # 是否开启接口文档
    enabled: true
  info:
    # 标题
    title: '系统模块接口文档'
    # 描述
    description: '系统模块接口描述'
yulaoai:
  url: https://cqlncs.12399.gov.cn:9003/glpt-api/stu/port/info
  publicKeyStr: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCF8cl+I/Di6bMnNoXqH2BHr9a2IDbnpkM6CZzZLfuzWQTHSmeQXMg+Xdq5iZWmvRjr+UJpZus5zfznXtr0zGKlwuLhYFch2SHfZF7/CooKJH8P0WytqMU3iK5DZTQ5NTfLhbaERNjFK83qlGe+yO8TNIsSApqItTIK+k1bGgso9wIDAQAB
  privateKeyStr: MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAJPdOBMtinAxWxtkM3PrzfYNpQgCy/tG+f2h8e+7gM1FnRjZXp/a0QTrEEuP89PFEhaalzqhm2UL9QBDXHpOxrRfIF/i+7GUMTcec3Ic1MH4aEwV99G4N16ElZ5tilryjYkeGC60tlHkqiZr4z8mbGJuh+KrYgrQGjYaR1/JDS0PAgMBAAECgYACn7teA8HgKdAPFWEgXn/iLO3PzZTnGaRYDuanNXGcsu6NwVW/ineJGc7JOH3ANVAT3JxwauvCCEtJvOhIN1upUtQVSBb/ad3t84B0Zrhj03B6gKKGhbKBZbBIQswUJ6QLut1lcLB9sFkOBPm9dYytDudtOqOCKiBSAdpUZPMBMQJBANSotVd2DbP6wUGPEltmhbxYE6Mg0sMBoE2DVu977FZAnp4jVuFZkbNJD6OJddxOtHpaFR90DDbaB4Pp+lwrB2cCQQCx/+V/y064E0f7np4C2jG016mQxSGwFgZfNDBcvlGdzBBhVPfxwQrZYzWXwc66kVQ8tj+N9/F4yUmfwX8eNmwZAkBivp/Np8x/GN1psqRkent41DDyG0iiMfwcYfMZKqK3/jNo3KH3655C8JbpaeenT4hwE6ohO8J8Kcs5joipCXXbAkABB4TRP2C+KcZhyyIIzDA7Zn/hiXJPbWEuDcQ11RftvK9fkLk6Wa5xYWR+8yz54TtGeptVe9iWpTtaxCizCQeJAkBWXngZSsy9WZmzfhE3ID3DoyR0eDvLgNp/y623AtcdJUNTcpnYq6CqPtR4O/0BejAJnerpxVR048uCKWhaKtm3
event:
    enable: false
    appCode: A50000000019370202312007001
    appSecret: f/UQ6yW+7f1nYxBorNf+mgVYpLvpkwkb6uQhQ1kXXlwfJAfVsP+M2w==
    eventType: DJ131207
    authKey: xxx
    authSecret: xxx
    username: xxx
    reportUrl: https://************:30032/api/push/eventReport/v1
    queryUrl: http://************:8799/dcqc/dcqc-gateway/dcqc-apiserver/gateway/api/orderquery/module/data/accept/api/exter/queryCompleteInfo
    tokenUrl: http://************:8799/dcqc/dcqc-gateway/dcqc-system/appKeySecret/apply/keyTokenByKey
    feedbackUrl: https://************:30032/api/push/dealLogPush/v1
ufa:
  url: https://cqlncs.12399.gov.cn:8093/

app:
  profile: /data/cdn/
  file-base: https://cqlncs.12399.gov.cn:8093

ykz:
  verifyToken: http://************/unionauth/token/checkToken/
  keyTokenByKeyUrl: http://************:8799/dcqc/dcqc-gateway/dcqc-system/appKeySecret/apply/keyTokenByKey
  accessTokenUrl: http://************:8799/dcqc/dcqc-gateway/dcqc-apiserver/gateway/api/third-api/access-token
  accountIdUrl: http://************:8799/dcqc/dcqc-gateway/dcqc-apiserver/gateway/api/third-api/account-id
  #愉快政sm4解密密钥
  appKey: eef9c2406c7e3da9c81939996657cf01
  appSecret: ecdbc6c0db34e51ca12a15510d80631b1b83b76c1560b11a55208025bb0fa763987c3f3e72e03d7b7b9d3f2722595055
  username: 390
ykb:
  url: http://ykbapp-test.cqdcg.com/gateway/open/api/do
  appIv: LlVkIzFtiT7omS56ILwdJA==
  appName: 博爱山城
  appId: 4dd50715-f024-4be7-aba2-2d8d31c62416
  appSecret: Z7F!0974
  appKey: RMahymDEd65SzxI7fijEqA==
#ufa-mobile:
#  url: https://cqlndx.12399.gov.cn:9003
#  #获取token
#  speedLoginMobileByAuthCode: /glpt-api/speedy/login/speedLoginMobileByAuthCode
#  #根据token获取用户权限等信息
#  getMobileLoginInfo: /glpt-api/speedy/login/getMobileLoginInfo
#  #查看用户信息接口
#  userProfile: /glpt-api/system/user/profile
#  #用户修改邮箱和昵称
#  editMobileUser: /glpt-api/speedy/login/editMobileUser
ufa-mobile:
  # 测试
  url: http://cqlndx.12399.gov.cn
  #获取token
  speedLoginMobileByAuthCode: /ykz-admin/speedy/login/speedLoginMobileByAuthCode
  #根据token获取用户权限等信息
  getMobileLoginInfo: /ykz-admin/speedy/login/getMobileLoginInfo
  #查看用户信息接口
  userProfile: /ykz-admin/system/user/profile
  #用户修改邮箱和昵称
  editMobileUser: /ykz-admin/speedy/login/editMobileUser