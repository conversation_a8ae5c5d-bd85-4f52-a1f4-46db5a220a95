# Tomcat
server:
  port: 9201

# Spring
spring:
  application:
    # 应用名称
    name: ows-ufa-system
  profiles:
    # 环境配置
    active: dev
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
# feign 配置
feign:
  sentinel:
    enabled: true
  okhttp:
    enabled: true
  httpclient:
    enabled: false
  client:
    config:
      default:
        connectTimeout: 10000
        readTimeout: 10000
  compression:
    request:
      enabled: true
      min-request-size: 8192
    response:
      enabled: true

# 暴露监控端点
management:
  endpoints:
    enabled-by-default: false
projectImg: /opt/applog/images/

logging:
  level:
    com.ows.ufa.system: debug
    org.mybatis: DEBUG