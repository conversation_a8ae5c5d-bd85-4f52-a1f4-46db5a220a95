<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ows.ufa.system.mapper.QuestionnaireMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ows.ufa.system.entity.Questionnaire">
        <id column="id" property="id" />
        <result column="title" property="title" />
        <result column="cover_image" property="coverImage" />
        <result column="initiator" property="initiator" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="fill_permission" property="fillPermission" />
        <result column="participation_limit" property="participationLimit" />
        <result column="allow_modify" property="allowModify" />
        <result column="collection_limit" property="collectionLimit" />
        <result column="status" property="status" />
        <result column="content_json" property="contentJson" />
        <result column="del_flag" property="delFlag" />
        <result column="dept_id" property="deptId" />
        <result column="create_at" property="createAt" />
        <result column="update_at" property="updateAt" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="app_name" property="appName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, title, cover_image, initiator, start_time, end_time, fill_permission, participation_limit, allow_modify, collection_limit, status, content_json, del_flag, dept_id, create_at, update_at, create_time, update_time
    </sql>
    <select id="querySubmitQuestionnaires" resultType="com.ows.ufa.system.vo.QuestionnaireVO">
        select tq.*,(select count(1) from t_questionnaire_submit tqs where create_at =#{req.userId} and tqs.questionnaire_id =tq.id) num from t_questionnaire tq
        WHERE tq.del_flag = 1 and tq.status >0
        <if test="req.title != null and req.title != ''">
            AND tq.title LIKE CONCAT('%', #{req.title}, '%')
        </if>
        <if test="req.submitStatus != null and req.submitStatus == 0">
            AND num>0 AND tq.status = 1
        </if>
        <if test="req.submitStatus != null and req.submitStatus == 1">
            AND num=0 AND tq.status = 1
        </if>
        <if test="req.submitStatus != null and req.submitStatus == 2">
            AND tq.status = 2
        </if>
        <if test="req.deptId != null">
            and ((tq.fill_permission=0 AND tq.dept_id in
            <foreach collection="req.ancestors" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>)
            or <![CDATA[(tq.fill_permission =1 and  #{req.deptId} = ANY(string_to_array(tq.fill_permission_dept_ids, '&&&')::bigint[]))) ]]>
        </if>
        <if test="req.deptId == null">
            and tq.fill_permission=0
        </if>

        order by tq.create_time desc
    </select>
    <select id="queryQuestionnaires" resultType="com.ows.ufa.system.vo.QuestionnaireVO">
        select tq.*,(select count(1) from t_questionnaire_submit tqs where del_flag=1 and tq.id=tqs.questionnaire_id) num from t_questionnaire tq
        where tq.del_flag = 1
        <if test="req.title != null and req.title != ''">
            AND tq.title LIKE CONCAT('%', #{req.title}, '%')
        </if>
        <if test="req.status != null">
            AND tq.status=#{req.status}
        </if>
        <if test="req.ancestors != null">
            AND tq.dept_id in
            <foreach collection="req.ancestors" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
        </if>
        <if test="req.appName != null">
            AND tq.app_name = #{req.appName}
        </if>
        order by tq.create_time desc
    </select>
    <select id="countSubmitQuestionnaires" resultType="com.ows.ufa.system.vo.QuestionnaireVO">
        select tq.* from t_questionnaire tq
        WHERE tq.del_flag = 1 and tq.status >0
        and ((tq.fill_permission=0 AND tq.dept_id in
            <foreach collection="req.ancestors" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>)
        or <![CDATA[(tq.fill_permission =1 and  #{req.deptId} = ANY(string_to_array(tq.fill_permission_dept_ids, '&&&')::bigint[]))) ]]>
        order by tq.create_time desc
    </select>

</mapper>