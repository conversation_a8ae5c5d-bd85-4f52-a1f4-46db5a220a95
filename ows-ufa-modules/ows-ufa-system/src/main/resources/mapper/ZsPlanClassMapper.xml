<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ows.ufa.system.mapper.ZsPlanClassMapper">

    <select id="selectSignUpNumber" resultType="com.ows.ufa.system.entity.ZsPlanClass">
        SELECT
            dept_name,
            ( COALESCE ( sum( back_num ), 0 )+ COALESCE ( sum( renew_pay_show_num ), 0 )+ COALESCE ( sum( sign_num ), 0 ) ) signNum
        FROM
            zs_plan_class
        WHERE
            plan_id = #{planId}
        GROUP BY
            dept_name
        ORDER BY
            signNum DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>
    <select id="selectMajorNumber" resultType="java.lang.Long">
        SELECT
            count( DISTINCT major_id )
        FROM
            zs_plan_class
        WHERE
            plan_id = #{planId}
          AND school_id = #{schoolId}
          <if test="deptId != null and deptId != ''">
              AND dept_id = #{deptId}
          </if>
    </select>

    <select id="selectSchoolIdByUserId" resultType="java.lang.String">
        SELECT
            school_id
        FROM
            (
                SELECT
                    zpc.is_grade_class,
                    zpc.school_id,
                    zpc.plan_id,
                    zps.stu_id,
                    zps.ID,
                    bs.stu_name,
                    bs.stu_sex,
                    bs.stu_phone,
                    bs.stu_card,
                    bs.stu_card_type,
                    bs.stu_birth,
                    zpc.dept_name,
                    zpc.class_name,
                    zpc.school_system,
                    zpc.school_term,
                    zpc.cr_name,
                    zpc.tch_name,
                    cst.class_time_week,
                    cst.class_time_span,
                    cst.start_time,
                    cst.end_time,
                    zps.create_time,
                    zps.is_transfer,
                    zps.stu_status,
                    zps.sign_source,
                    zpc.plan_id,
                    zps.class_id,
                    zps.change_type,
                    zpc.major_name,
                    zpc.course_name,
                    zps.is_pay,
                    zpc.course_fees
                FROM
                    zs_plan_student zps
                        LEFT JOIN base_student bs ON bs.ID = zps.stu_id
                        LEFT JOIN zs_plan_class zpc ON zpc.ID = zps.class_id
                        LEFT JOIN base_school_dept bsd ON bsd.ID = zpc.dept_id
                        LEFT JOIN class_school_time cst ON cst.class_id = zpc.ID
                WHERE
                    ( zps.is_pay = '1' OR zps.change_type = '2' )
                  AND zps.is_flag = '0'

                  AND zps.stu_id = #{userId}
                ORDER BY
                    zps.create_time DESC,
                    zps.ID
            )
        GROUP BY
            school_id
    </select>
</mapper>