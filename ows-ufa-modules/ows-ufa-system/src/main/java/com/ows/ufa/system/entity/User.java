package com.ows.ufa.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 用户信息表
 *
 * <AUTHOR>
 * @since 2025-02-17
 */
@Data
@TableName("sys_user")
@Schema(description ="用户信息表实体")
public class User implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "微信公众号认证")
    private String openid;

    @Schema(description = "对应部门id")
    private String topOrgId;

    @Schema(description = "对应学生表或教师表ID或注册用户ID(学生用户该ID存的是学生表ID,教师i用户该ID存的是学生表ID)")
    private String detailId;

    @Schema(description = "用户账号")
    private String userName;

    @Schema(description = "用户昵称")
    private String nickName;

    @Schema(description = "身份证号")
    private String userCard;

    @Schema(description = "用户类型[00平台管理员|01老干局用户|02学校用户|03注册学员|09其他用户]")
    private String userType;

    @Schema(description = "用户邮箱")
    private String email;

    @Schema(description = "手机号码")
    private String phonenumber;

    @Schema(description = "用户性别[1男2女3未知]")
    private String sex;

    @Schema(description = "头像地址")
    private String avatar;

    @Schema(description = "密码")
    private String password;

    @Schema(description = "最后登录IP")
    private String loginIp;

    @Schema(description = "最后登录时间")
    private LocalDateTime loginDate;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "部门名称(逗号分隔)")
    private String deptNames;

    @Schema(description = "岗位名称(逗号分隔)")
    private String postNames;

    @Schema(description = "角色名称(逗号分隔)")
    private String roleNames;

    @Schema(description = "创建者")
    private String createBy;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新者")
    private String updateBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "帐号状态[0正常 1停用]")
    private String status;

    @Schema(description = "删除标志[0代表存在 2代表删除]")
    private String delFlag;

    @Schema(description = "渝快政用户唯一标识")
    private Long accountId;

    @Schema(description = "渝快办用户唯一标识")
    private String ykbUserId;


}