package com.ows.ufa.system.service;

import com.ows.ufa.system.domain.request.H5BaseSchoolReq;
import com.ows.ufa.system.domain.response.H5BaseSchoolCourseRsp;
import com.ows.ufa.system.domain.response.H5BaseSchoolRsp;
import com.ows.ufa.system.entity.BaseSchool;

import java.util.List;


public interface H5SchoolInfoService {

    List<BaseSchool> queryAll(H5BaseSchoolReq h5BaseSchoolReq);

    List<BaseSchool> queryMySchool(H5BaseSchoolReq h5BaseSchoolReq);

    List<H5BaseSchoolRsp> queryMyStuDoc(H5BaseSchoolReq h5BaseSchoolReq);

    List<H5BaseSchoolCourseRsp> queryMyStuClassList(H5BaseSchoolReq h5BaseSchoolReq);
}
