package com.ows.ufa.system.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 学生家庭成员实体
 *
 * <AUTHOR>
 * @since 2025-08-06
 */
@Data
@TableName("base_student_family")
@Schema(description = "学生家庭成员实体")
public class BaseStudentFamily implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "家庭成员表")
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    @Schema(description = "学生ID")
    @TableField("stu_id")
    private String stuId;

    @Schema(description = "成员关系[1夫妻 2子女 3其他]")
    @TableField("member_relation")
    private String memberRelation;

    @Schema(description = "成员姓名")
    @TableField("member_name")
    private String memberName;

    @Schema(description = "成员手机号")
    @TableField("member_phone")
    private String memberPhone;

    @Schema(description = "成员工作单位")
    @TableField("member_company")
    private String memberCompany;

    @Schema(description = "创建人")
    @TableField("create_by")
    private String createBy;

    @Schema(description = "创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    @Schema(description = "修改人")
    @TableField("update_by")
    private String updateBy;

    @Schema(description = "修改时间")
    @TableField("update_time")
    private LocalDateTime updateTime;
}