package com.ows.ufa.system.controller;

import com.ows.ufa.common.core.domain.R;
import com.ows.ufa.common.security.utils.SecurityUtils;
import com.ows.ufa.system.api.model.LoginUser;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 调试控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/debug")
public class DebugController {

    @GetMapping("/loginStatus")
    public R<Map<String, Object>> getLoginStatus() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 检查Token是否存在
            String token = SecurityUtils.getToken();
            result.put("hasToken", token != null);
            result.put("tokenLength", token != null ? token.length() : 0);
            
            // 尝试获取登录用户信息
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (loginUser != null) {
                result.put("hasLoginUser", true);
                result.put("userid", loginUser.getUserid());
                result.put("thirdUserid", loginUser.getThirdUserid());
                result.put("username", loginUser.getUsername());
                result.put("isThird", loginUser.isThird());
                result.put("thirdDeptId", loginUser.getThirdDeptId());
                
                // 检查SysUser信息
                if (loginUser.getSysUser() != null) {
                    result.put("hasSysUser", true);
                    result.put("sysUserUserId", loginUser.getSysUser().getUserId());
                    result.put("sysUserThirdUserId", loginUser.getSysUser().getThirdUserId());
                } else {
                    result.put("hasSysUser", false);
                }
            } else {
                result.put("hasLoginUser", false);
            }
            
            // 尝试直接调用SecurityUtils方法
            try {
                String thirdUserId = SecurityUtils.getThirdUserid();
                result.put("thirdUseridFromSecurityUtils", thirdUserId);
            } catch (Exception e) {
                result.put("thirdUseridError", e.getMessage());
            }
            
        } catch (Exception e) {
            result.put("error", e.getMessage());
            result.put("stackTrace", e.getStackTrace());
        }
        
        return R.ok(result);
    }
}
