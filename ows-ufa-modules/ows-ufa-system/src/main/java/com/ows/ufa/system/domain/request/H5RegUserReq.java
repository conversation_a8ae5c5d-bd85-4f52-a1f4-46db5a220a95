package com.ows.ufa.system.domain.request;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户信息表
 *
 * <AUTHOR>
 * @since 2025-02-17
 */
@Data
@Schema(description ="用户信息表实体")
public class H5RegUserReq implements Serializable {

    @Schema(description = "学员姓名")
    private String stuName;

    @Schema(description = "证件类型")
    private String stuCardType;

    @Schema(description = "加密身份证号")
    private String stuCard;

    @Schema(description = "性别")
    private String stuSex;

    @Schema(description = "出生年月")
    private String stuBirth;

//    @Schema(description = "加密联系电话")
//    private String jmStuPhone;

    @Schema(description = "联系电话")
    private String stuPhone;

    @Schema(description = "居住情况")
    private String stuLiveState;

    @Schema(description = "文化程度")
    private String stuEduLevel;

    @Schema(description = "政治面貌")
    private String stuPolitical;

    @Schema(description = "退休状态")
    private String stuRetireState;

    @Schema(description = "原职务")
    private String stuHisJob;

    @Schema(description = "原岗位")
    private String stuHisPost;

    @Schema(description = "原职称")
    private String stuHisTitle;

    @Schema(description = "原工作单位")
    private String stuHisCompany;

    @Schema(description = "是否上过老年大学")
    private String isHaveElderCollege;

    @Schema(description = "家人是否同意")
    private String isAgree;

    @Schema(description = "是否购买保险")
    private String isBuyInsure;

    @Schema(description = "能否坚持学习")
    private String stuHealthState;

    @Schema(description = "既往病史")
    private String isHaveMedical;

    @Schema(description = "既往病史详情")
    private String medicalDesc;

    @Schema(description = "现居住地址")
    private String stuAddr;

    @Schema(description = "应急联系人列表")
    private List<FamilyMember> familyList;

//    @Schema(description = "数字验证码")
//    private String code;

//    @Schema(description = "唯一key")
//    private String uuid;

    @Data
    @Schema(description = "应急联系人信息")
    public static class FamilyMember {

        @Schema(description = "成员关系")
        private String memberRelation;

        @Schema(description = "姓名")
        private String memberName;

        @Schema(description = "手机号")
        private String memberPhone;

        @Schema(description = "工作单位")
        private String memberCompany;
    }


}