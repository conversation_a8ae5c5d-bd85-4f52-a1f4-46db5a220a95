package com.ows.ufa.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.QueryChainWrapper;
import com.ows.ufa.common.core.exception.ServiceException;
import com.ows.ufa.common.core.exception.auth.NotLoginException;
import com.ows.ufa.common.core.exception.user.UserException;
import com.ows.ufa.common.security.utils.SecurityUtils;
import com.ows.ufa.system.api.domain.SysUser;
import com.ows.ufa.system.config.DataSource;
import com.ows.ufa.system.domain.request.H5BaseSchoolReq;
import com.ows.ufa.system.domain.response.H5BaseSchoolCourseRsp;
import com.ows.ufa.system.domain.response.H5BaseSchoolRsp;
import com.ows.ufa.system.entity.BaseSchool;
import com.ows.ufa.system.entity.BaseStudent;
import com.ows.ufa.system.entity.User;
import com.ows.ufa.system.enums.DataSourceType;
import com.ows.ufa.system.mapper.BaseSchoolMapper;
import com.ows.ufa.system.mapper.BaseStudentMapper;
import com.ows.ufa.system.mapper.SysUserMapper;
import com.ows.ufa.system.mapper.UserMapper;
import com.ows.ufa.system.service.BaseStudentService;
import com.ows.ufa.system.service.H5SchoolInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class H5SchoolInfoServiceImpl implements H5SchoolInfoService {

    @Autowired
    BaseSchoolMapper baseSchoolMapper;
    @Autowired
    BaseStudentMapper baseStudentMapper;
    @Autowired
    UserMapper userMapper;

    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<BaseSchool> queryAll(H5BaseSchoolReq h5BaseSchoolReq) {

        List<BaseSchool> list = baseSchoolMapper.queryAll(h5BaseSchoolReq);

        return list;
    }
    public String queryStuidByUserId(Long userId){
        if (Objects.isNull(userId)){
                throw new NotLoginException("请先登录");
        }
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getUserId,userId);
        User user = userMapper.selectOne(queryWrapper);
        if (Objects.isNull(user)){
            return null;
        }
        if ( Objects.isNull(user.getUserCard())){
            return null;
        }

        LambdaQueryWrapper<BaseStudent> querystu = new LambdaQueryWrapper<>();
        querystu.eq(BaseStudent::getStuCard,user.getUserCard());


        BaseStudent stud = baseStudentMapper.selectOne(querystu);
        if (Objects.isNull(stud)){
            return null;
        }
        return stud.getId();
    }

    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<BaseSchool> queryMySchool(H5BaseSchoolReq h5BaseSchoolReq) {
        String stuid = this.queryStuidByUserId(SecurityUtils.getLoginUser().getSysUser().getUserId());
        List<BaseSchool> list = baseSchoolMapper.queryMySchool(stuid,h5BaseSchoolReq);
        return list;
    }


    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<H5BaseSchoolRsp> queryMyStuDoc(H5BaseSchoolReq h5BaseSchoolReq) {
        String stuid = this.queryStuidByUserId(SecurityUtils.getLoginUser().getSysUser().getUserId());
        List<H5BaseSchoolRsp> list = baseSchoolMapper.queryMyStuDoc(stuid,h5BaseSchoolReq);
        return list;
    }

    @Override
    public List<H5BaseSchoolCourseRsp> queryMyStuClassList(H5BaseSchoolReq h5BaseSchoolReq) {
        String stuid = this.queryStuidByUserId(SecurityUtils.getLoginUser().getSysUser().getUserId());
        List<H5BaseSchoolCourseRsp> list = baseSchoolMapper.queryMyStuClassList(stuid,h5BaseSchoolReq);
        return list;
    }
}
