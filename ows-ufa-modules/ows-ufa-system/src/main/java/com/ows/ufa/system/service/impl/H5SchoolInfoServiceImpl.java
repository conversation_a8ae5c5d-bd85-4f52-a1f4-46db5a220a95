package com.ows.ufa.system.service.impl;

import com.ows.ufa.common.security.utils.SecurityUtils;
import com.ows.ufa.system.config.DataSource;
import com.ows.ufa.system.domain.request.H5BaseSchoolReq;
import com.ows.ufa.system.domain.response.H5BaseSchoolCourseRsp;
import com.ows.ufa.system.domain.response.H5BaseSchoolRsp;
import com.ows.ufa.system.entity.BaseSchool;
import com.ows.ufa.system.enums.DataSourceType;
import com.ows.ufa.system.mapper.BaseSchoolMapper;
import com.ows.ufa.system.service.H5SchoolInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class H5SchoolInfoServiceImpl implements H5SchoolInfoService {

    @Autowired
    BaseSchoolMapper baseSchoolMapper;

    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<BaseSchool> queryAll(H5BaseSchoolReq h5BaseSchoolReq) {

        List<BaseSchool> list = baseSchoolMapper.queryAll(h5BaseSchoolReq);

        return list;
    }

    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<BaseSchool> queryMySchool(H5BaseSchoolReq h5BaseSchoolReq) {
        String userid = SecurityUtils.getThirdUserid();
        List<BaseSchool> list = baseSchoolMapper.queryMySchool(userid,h5BaseSchoolReq);
        return list;
    }

    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<H5BaseSchoolRsp> queryMyStuDoc(H5BaseSchoolReq h5BaseSchoolReq) {
        String userid = SecurityUtils.getThirdUserid();
        List<H5BaseSchoolRsp> list = baseSchoolMapper.queryMyStuDoc(userid,h5BaseSchoolReq);
        return list;
    }

    @Override
    public List<H5BaseSchoolCourseRsp> queryMyStuClassList(H5BaseSchoolReq h5BaseSchoolReq) {
        String userid = SecurityUtils.getThirdUserid();
        List<H5BaseSchoolCourseRsp> list = baseSchoolMapper.queryMyStuClassList(userid,h5BaseSchoolReq);
        return list;
    }
}
