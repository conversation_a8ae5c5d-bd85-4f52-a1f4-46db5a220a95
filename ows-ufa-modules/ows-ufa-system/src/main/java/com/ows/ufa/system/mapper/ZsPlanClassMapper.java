package com.ows.ufa.system.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ows.ufa.common.datasource.annotation.Slave;
import com.ows.ufa.system.entity.ZsPlanClass;
import com.ows.ufa.system.entity.ZsPlanStudent;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 在籍学员 Mapper 接口
 * </p>
 *
 */
@Mapper
public interface ZsPlanClassMapper extends BaseMapper<ZsPlanClass> {

    public List<ZsPlanClass> selectSignUpNumber(@Param(value = "planId")String planId,
                                                @Param(value = "limit")Integer limit);

    public Long selectMajorNumber(@Param(value = "planId")String planId,
                                  @Param(value = "schoolId")String schoolId,
                                  @Param(value = "deptId")String deptId);

    @Slave
    String selectSchoolIdByUserId(String userId);

}