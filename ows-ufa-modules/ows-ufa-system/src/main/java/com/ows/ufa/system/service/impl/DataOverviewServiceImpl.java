package com.ows.ufa.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ows.ufa.common.core.exception.ServiceException;
import com.ows.ufa.common.core.utils.DateUtils;
import com.ows.ufa.common.security.utils.SecurityUtils;
import com.ows.ufa.system.api.model.LoginUser;
import com.ows.ufa.system.config.DataSource;
import com.ows.ufa.system.domain.response.*;
import com.ows.ufa.system.domain.vo.StudentStatisticsVo;
import com.ows.ufa.system.entity.*;
import com.ows.ufa.system.enums.DataSourceType;
import com.ows.ufa.system.mapper.*;
import com.ows.ufa.system.service.DataOverviewService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据概览 服务层处理
 */
@Service
public class DataOverviewServiceImpl implements DataOverviewService {

    @Autowired
    private ZsPlanStudentMapper zsPlanStudentMapper;

    @Autowired
    private ZsPlanMapper zsPlanMapper;

    @Autowired
    private BaseSchoolDeptMapper baseSchoolDeptMapper;

    @Autowired
    private BaseSchoolTeacherMapper baseSchoolTeacherMapper;

    @Autowired
    private BaseClassroomMapper baseClassroomMapper;

    @Autowired
    private ZsPlanClassMapper zsPlanClassMapper;

    @Autowired
    private ZsOrderItemMapper zsOrderItemMapper;

    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public DataOverviewHomeNumber homePageNumber() {
        DataOverviewHomeNumber res = new DataOverviewHomeNumber();
        String schoolId = SecurityUtils.getLoginUser().getSysUser().getTopOrgId();
        //招生计划id
        String planId = getPlanId(schoolId);
        if (StringUtils.isEmpty(planId)) {
            // 如果planId为空，返回数据为0的对象
            res.setZsPlanStudentTotal(0L);
            res.setDeptNumber(0L);
            res.setTeacherNumber(0L);
            res.setClassroomNumber(0L);
            res.setClassNumber(0L);
            return res;
        }
        //查询在籍学员总数
        Long zsPlanStudentTotal = zsPlanStudentMapper.getZsPlanStudentTotal(schoolId, planId);
        res.setZsPlanStudentTotal(zsPlanStudentTotal);
        //院系数量
        LambdaQueryWrapper<BaseSchoolDept> baseSchoolDeptLambdaQueryWrapper = new LambdaQueryWrapper<>();
        baseSchoolDeptLambdaQueryWrapper.eq(BaseSchoolDept::getSchoolId, schoolId);
        Long deptNumber = baseSchoolDeptMapper.selectCount(baseSchoolDeptLambdaQueryWrapper);
        res.setDeptNumber(deptNumber);
        //教师数量
        LambdaQueryWrapper<BaseSchoolTeacher> baseSchoolTeacherLambdaQueryWrapper = new LambdaQueryWrapper<>();
        baseSchoolTeacherLambdaQueryWrapper.eq(BaseSchoolTeacher::getSchoolId, schoolId);
        Long teacherNumber = baseSchoolTeacherMapper.selectCount(baseSchoolTeacherLambdaQueryWrapper);
        res.setTeacherNumber(teacherNumber);
        //教室数量
        LambdaQueryWrapper<BaseClassroom> baseClassroomLambdaQueryWrapper = new LambdaQueryWrapper<>();
        baseClassroomLambdaQueryWrapper.eq(BaseClassroom::getSchoolId, schoolId);
        Long classroomNumber = baseClassroomMapper.selectCount(baseClassroomLambdaQueryWrapper);
        res.setClassroomNumber(classroomNumber);
        //班级数量
        LambdaQueryWrapper<ZsPlanClass> zsPlanClassLambdaQueryWrapper = new LambdaQueryWrapper<>();
        zsPlanClassLambdaQueryWrapper.eq(ZsPlanClass::getSchoolId, schoolId);
        zsPlanClassLambdaQueryWrapper.eq(ZsPlanClass::getPlanId, planId);
        Long classNumber = zsPlanClassMapper.selectCount(zsPlanClassLambdaQueryWrapper);
        res.setClassNumber(classNumber);
        return res;
    }

    /**
     * 缴费统计
     *
     * @param type 0-包含今天的近七天 1-包含当月 2-包含当年
     * @return
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public DataOverviewHomePay homePagePay(Integer type) {

        DataOverviewHomePay res = new DataOverviewHomePay();
        String schoolId = SecurityUtils.getLoginUser().getSysUser().getTopOrgId();
        //招生计划id
        String planId = getPlanId(schoolId);
        if (StringUtils.isEmpty(planId)) {
            // 如果planId为空，返回空数据的对象
            res.setDateName(new ArrayList<>());
            res.setNums(new ArrayList<>());
            return res;
        }

        List<Map<String, Date>> startEndDates = new ArrayList<>();

        SimpleDateFormat sdf = null;

        if (type == 0) {
            sdf = new SimpleDateFormat("dd日");
            //获取包含今天的近七天起止时间
            startEndDates = DateUtils.getSevenDaysDates();
        } else if (type == 1) {
            sdf = new SimpleDateFormat("MM月");
            //获取包含当月的起止时间
            startEndDates = DateUtils.getCurrentMonthDates();
        } else if (type == 2) {
            sdf = new SimpleDateFormat("yyyy年");
            //获取包含当年的起止时间
            startEndDates = DateUtils.getCurrentYearDates();
        } else {
            return null;
        }
        List<String> dateName = new ArrayList<>();
        List<BigDecimal> nums = new ArrayList<>();
        for (Map<String, Date> startEndDate : startEndDates) {
            Date startDate = startEndDate.get("startDate");
            Date endDate = startEndDate.get("endDate");
            BigDecimal amount = zsOrderItemMapper.selectOrderAmountByPayTime(planId, startDate, endDate);
            nums.add(amount == null ? BigDecimal.ZERO : amount);
            dateName.add(sdf.format(startDate));
        }

        res.setDateName(dateName);
        res.setNums(nums);
        return res;
    }

    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public DataOverviewHomeSignUp homePageSignUp() {
        DataOverviewHomeSignUp res = new DataOverviewHomeSignUp();

        String schoolId = SecurityUtils.getLoginUser().getSysUser().getTopOrgId();
        //招生计划id
        String planId = getPlanId(schoolId);
        if (StringUtils.isEmpty(planId)) {
            // 如果planId为空，返回空数据的对象
            res.setDeptName(new ArrayList<>());
            res.setNums(new ArrayList<>());
            return res;
        }

        List<ZsPlanClass> zsPlanClasses = zsPlanClassMapper.selectSignUpNumber(planId, 5);

        List<String> deptName = new ArrayList<>();
        List<Integer> nums = new ArrayList<>();

        for (ZsPlanClass zsPlanClass : zsPlanClasses) {
            deptName.add(zsPlanClass.getDeptName());
            nums.add(zsPlanClass.getSignNum());
        }
        res.setDeptName(deptName);
        res.setNums(nums);
        return res;
    }

    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public DataOverviewDeptSituation deptSituation(String deptId) {
        DataOverviewDeptSituation res = new DataOverviewDeptSituation();

        //移动端目前没有登录schoolId先写死
        String schoolId = SecurityUtils.getLoginUser().getSysUser().getTopOrgId();
        //招生计划id
        String planId = getPlanId(schoolId);
        if (StringUtils.isEmpty(planId)) {
            // 如果planId为空，返回数据为0的对象
            res.setTeacherNumber(0L);
            res.setClassNumber(0L);
            res.setMajorNumber(0L);
            res.setStudentNumber(0L);
            return res;
        }

        //教师数量
        LambdaQueryWrapper<BaseSchoolTeacher> baseSchoolTeacherLambdaQueryWrapper = new LambdaQueryWrapper<>();
        baseSchoolTeacherLambdaQueryWrapper.eq(BaseSchoolTeacher::getSchoolId, schoolId);
        if (StringUtils.isNotEmpty(deptId)) {
            baseSchoolTeacherLambdaQueryWrapper.eq(BaseSchoolTeacher::getDeptId, deptId);
        }
        Long teacherNumber = baseSchoolTeacherMapper.selectCount(baseSchoolTeacherLambdaQueryWrapper);
        res.setTeacherNumber(teacherNumber);

        //班级数量
        LambdaQueryWrapper<ZsPlanClass> zsPlanClassLambdaQueryWrapper = new LambdaQueryWrapper<>();
        zsPlanClassLambdaQueryWrapper.eq(ZsPlanClass::getSchoolId, schoolId);
        zsPlanClassLambdaQueryWrapper.eq(ZsPlanClass::getPlanId, planId);
        if (StringUtils.isNotEmpty(deptId)) {
            zsPlanClassLambdaQueryWrapper.eq(ZsPlanClass::getDeptId, deptId);
        }
        Long classNumber = zsPlanClassMapper.selectCount(zsPlanClassLambdaQueryWrapper);
        res.setClassNumber(classNumber);

        //课程数量
        Long majorNumber = zsPlanClassMapper.selectMajorNumber(planId, schoolId, deptId);
        res.setMajorNumber(majorNumber);

        //学员数量
        Long studentNumber = zsPlanStudentMapper.getZsPlanStudentNumber(schoolId, planId, deptId);
        res.setStudentNumber(studentNumber);

        return res;
    }

    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public List<BaseSchoolDept> dept() {
        String schoolId = SecurityUtils.getLoginUser().getSysUser().getTopOrgId();
        LambdaQueryWrapper<BaseSchoolDept> baseSchoolDeptLambdaQueryWrapper = new LambdaQueryWrapper<>();
        baseSchoolDeptLambdaQueryWrapper.eq(BaseSchoolDept::getSchoolId, schoolId);
        baseSchoolDeptLambdaQueryWrapper.orderByAsc(BaseSchoolDept::getDeptSort);
        List<BaseSchoolDept> baseSchoolDepts = baseSchoolDeptMapper.selectList(baseSchoolDeptLambdaQueryWrapper);
        return baseSchoolDepts;
    }

    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public DataOverviewDeptSignUp deptSignUp() {
        DataOverviewDeptSignUp res = new DataOverviewDeptSignUp();

        String schoolId = SecurityUtils.getLoginUser().getSysUser().getTopOrgId();

        //招生计划id
        String planId = getPlanId(schoolId);
        if (StringUtils.isEmpty(planId)) {
            // 如果planId为空，返回默认数据的对象
            res.setTotalNumber(0L);
            res.setData(new ArrayList<>());
            return res;
        }

        List<ZsPlanClass> zsPlanClasses = zsPlanClassMapper.selectSignUpNumber(planId, null);

        if (zsPlanClasses == null || zsPlanClasses.size() == 0) {
            return res;
        }
        Long totalNumber = 0L;
        for (ZsPlanClass zsPlanClass : zsPlanClasses) {
            totalNumber += zsPlanClass.getSignNum();
        }
        res.setTotalNumber(totalNumber);
        res.setData(zsPlanClasses);
        return res;
    }

    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public DataOverviewStudentStatistics student() {
        DataOverviewStudentStatistics res = new DataOverviewStudentStatistics();

        String schoolId = SecurityUtils.getLoginUser().getSysUser().getTopOrgId();

        //招生计划id
        String planId = getPlanId(schoolId);
        if (StringUtils.isEmpty(planId)) {
            // 如果planId为空，返回空统计数据的对象
            res.setStudentPolitical(new ArrayList<>());
            res.setStudentRetireState(new ArrayList<>());
            res.setStudentEduLevel(new ArrayList<>());
            res.setStudentLiveState(new ArrayList<>());
            res.setStudentSex(new ArrayList<>());
            res.setStudentAge(new ArrayList<>());
            return res;
        }

        //政治面貌统计
        List<StudentStatisticsVo> studentPolitical = zsPlanStudentMapper.selectStudentPolitical(schoolId, planId);
        res.setStudentPolitical(calculatePercentage(studentPolitical));
        //退休状态统计
        List<StudentStatisticsVo> studentRetireState = zsPlanStudentMapper.selectStudentRetireState(schoolId, planId);
        res.setStudentRetireState(calculatePercentage(studentRetireState));
        //学历统计
        List<StudentStatisticsVo> studentEduLevel = zsPlanStudentMapper.selectStudentEduLevel(schoolId, planId);
        res.setStudentEduLevel(calculatePercentage(studentEduLevel));
        //居住情况统计
        List<StudentStatisticsVo> studentLiveState = zsPlanStudentMapper.selectStudentLiveState(schoolId, planId);
        res.setStudentLiveState(calculatePercentage(studentLiveState));
        //性别统计
        List<StudentStatisticsVo> studentSex = zsPlanStudentMapper.selectStudentSex(schoolId, planId);
        //判断是否有包含男女
        if (studentSex == null || studentSex.size() == 0) {
            studentSex = new ArrayList<>();
            studentSex.add(new StudentStatisticsVo("1", 0L));
            studentSex.add(new StudentStatisticsVo("2", 0L));
        } else if (studentSex.size() == 1) {
            if (studentSex.get(0).getData().equals("1")) {
                studentSex.add(new StudentStatisticsVo("2", 0L));
            } else if (studentSex.get(0).getData().equals("2")) {
                studentSex.add(new StudentStatisticsVo("1", 0L));
            } else {
                studentSex.add(new StudentStatisticsVo("1", 0L));
                studentSex.add(new StudentStatisticsVo("2", 0L));
            }
        }
        List<StudentStatisticsVo> sexs = calculatePercentage(studentSex);
        //男数据
        StudentStatisticsVo sex1 = sexs.stream().filter(t -> t.getData().equals("1")).collect(Collectors.toList()).get(0);
        //女数据
        StudentStatisticsVo sex2 = sexs.stream().filter(t -> t.getData().equals("2")).collect(Collectors.toList()).get(0);
        sex1.setOther(sex2.getNum());
        sex2.setOther(sex1.getNum());
        //只返回男女
        res.setStudentSex(new ArrayList<>(Arrays.asList(sex1, sex2)));
        //年龄统计
        //获取所有身份证号
        List<StudentStatisticsVo> studentCard = zsPlanStudentMapper.selectStudentCard(schoolId, planId);
        //计算年龄
        if (studentCard == null || studentCard.size() == 0) {
            res.setStudentAge(new ArrayList<>());
        } else {
            //将身份证号转换为年龄
            for (StudentStatisticsVo item : studentCard) {
                if (StringUtils.isEmpty(item.getData())) {
                    continue;
                }
                item.setNum(Long.parseLong(calculateAge(item.getData(), null) + ""));
            }
            //将年龄区分 年龄[1 50岁以下 2 50-59岁 3 60-69岁 4 70岁以上]
            StudentStatisticsVo age50Before = new StudentStatisticsVo("1", studentCard.stream().filter(p -> p.getNum() < 50L).count());
            StudentStatisticsVo age50_59 = new StudentStatisticsVo("2", studentCard.stream().filter(p -> p.getNum() >= 50L && p.getNum() <= 59L).count());
            StudentStatisticsVo age60_69 = new StudentStatisticsVo("3", studentCard.stream().filter(p -> p.getNum() >= 60L && p.getNum() <= 69L).count());
            StudentStatisticsVo age70After = new StudentStatisticsVo("4", studentCard.stream().filter(p -> p.getNum() >= 70L).count());
            List<StudentStatisticsVo> newStudentStatisticsVo = new ArrayList<>(Arrays.asList(age50Before, age50_59, age60_69, age70After));
            res.setStudentAge(calculatePercentage(newStudentStatisticsVo));
        }
        return res;
    }

    /**
     * 根据身份证号或指定的出生日期计算年龄
     *
     * @param idCard       身份证号码（18位），如果为null则使用指定的出生日期
     * @param birthDateStr 指定的出生日期，格式为"yyyy-MM-dd"，当idCard为null时使用
     * @return 年龄，如果输入无效则返回-1
     */
    private int calculateAge(String idCard, String birthDateStr) {
        LocalDate birthDate;

        if (idCard != null && idCard.length() == 18) {
            // 从身份证号中提取出生日期
            String idBirthDateStr = idCard.substring(6, 14);
            birthDate = LocalDate.parse(idBirthDateStr, DateTimeFormatter.ofPattern("yyyyMMdd"));
        } else if (StringUtils.isNoneBlank(birthDateStr)) {
            // 使用指定的出生日期
            birthDate = LocalDate.parse(birthDateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        } else {
            return -1;
        }

        LocalDate currentDate = LocalDate.now();
        if ((birthDate != null) && (!birthDate.isAfter(currentDate))) {
            Period period = Period.between(birthDate, currentDate);
            return period.getYears();
        } else {
            return -1;
        }
    }

    /**
     * 计算百分比
     *
     * @return
     */
    private List<StudentStatisticsVo> calculatePercentage(List<StudentStatisticsVo> data) {
        if (data == null || data.size() == 0) {
            return new ArrayList<>();
        }
        //计算总和
        Long total = data.stream().mapToLong(StudentStatisticsVo::getNum).sum();

        data.forEach(item -> {
            if (total != 0L) {
                //计算百分百，保留一位小数
                item.setPercentage(
                        new BigDecimal(item.getNum()  * 100.0 / total)
                                .setScale(1, RoundingMode.HALF_UP)
                                .stripTrailingZeros()
                                .toPlainString() + "%"
                );
            } else {
                item.setPercentage(BigDecimal.ZERO.stripTrailingZeros().toPlainString() + "%");
            }
        });
        return data;
    }

    private String getPlanId(String schoolId) {
        LambdaQueryWrapper<ZsPlan> zsPlanLambdaQueryWrapper = new LambdaQueryWrapper<>();
        zsPlanLambdaQueryWrapper.eq(ZsPlan::getSchoolId, schoolId);
        zsPlanLambdaQueryWrapper.eq(ZsPlan::getStatus, 5);
        ZsPlan zsPlan = zsPlanMapper.selectOne(zsPlanLambdaQueryWrapper);
        if (zsPlan == null) {
            return null;
        }
        return zsPlan.getId();
    }
}
