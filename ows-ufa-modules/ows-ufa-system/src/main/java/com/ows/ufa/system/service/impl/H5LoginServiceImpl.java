package com.ows.ufa.system.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.digital.szzz.gateway.sdk.bean.GatewayResponse;
import com.ows.ufa.common.core.enums.SensitiveType;
import com.ows.ufa.common.core.exception.ServiceException;
import com.ows.ufa.common.core.utils.DataTransfer;
import com.ows.ufa.common.core.utils.SensitiveUtil;
import com.ows.ufa.common.core.utils.StringUtils;
import com.ows.ufa.common.core.utils.uuid.IdUtils;
import com.ows.ufa.common.security.service.TokenService;
import com.ows.ufa.common.security.utils.SecurityUtils;
import com.ows.ufa.system.api.domain.SysUser;
import com.ows.ufa.system.api.model.LoginUser;
import com.ows.ufa.system.config.DataSource;
import com.ows.ufa.system.domain.request.H5EditMobileUserRequest;
import com.ows.ufa.system.domain.request.H5RegUserReq;
import com.ows.ufa.system.entity.BaseStudent;
import com.ows.ufa.system.entity.BaseStudentFamily;
import com.ows.ufa.system.entity.User;
import com.ows.ufa.system.enums.DataSourceType;
import com.ows.ufa.system.mapper.BaseStudentFamilyMapper;
import com.ows.ufa.system.mapper.BaseStudentMapper;
import com.ows.ufa.system.mapper.UserMapper;
import com.ows.ufa.system.service.H5LoginService;
import com.ows.ufa.system.service.OpenService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 数据概览 服务层处理
 *
 */
@Slf4j
@Service
public class H5LoginServiceImpl implements H5LoginService
{

    @Autowired
    private OpenService openService;
    @Autowired
    private BaseStudentMapper baseStudentMapper;
    @Autowired
    private BaseStudentFamilyMapper baseStudentFamilyMapper;
    @Autowired
    private UserMapper userMapper;

    @Autowired
    private TokenService tokenService;


    @Override
    public String getTokenByCode(String authCode) {
        String body = openService.speedLoginMobileByAuthCode(authCode);
        log.debug("获取token的返回体是："+body);
        if(StringUtils.isNotEmpty(body)){
            JSONObject result = JSON.parseObject(body);
            String status = result.getString("status");
            if(status.equals("1")){
                //成功
                return result.getString("token");
            }else{
                //失败
                String msg = result.getString("msg");
                if(StringUtils.isNotEmpty(msg)){
                    throw new ServiceException(result.getString("msg"));
                }else{
                    throw new ServiceException("获取token失败");
                }
            }
        }else{
            throw new ServiceException("获取token失败");
        }
    }

    /**
     * 根据token获取用户权限等信息
     *
     * @param token
     * @return
     */
    @Override
    public Map<String,Object> getMobileLoginInfo(String token) {
        Map<String,Object> res = openService.getMobileLoginInfo(token);
        if(res != null){
            String code = String.valueOf(res.get("code"));
            if(code != null && code.equals("200")){
                res.remove("code");
                res.remove("msg");
                return res;
            }else{
                throw new ServiceException("获取用户权限信息失败");
            }
        }else{
            throw new ServiceException("获取用户权限信息失败");
        }
    }

    /**
     * 查看用户信息
     *
     * @param token
     * @return
     */
    @Override
    public Object userProfile(String token) {
        Map<String,Object> res = openService.userProfile(token);
        if(res != null){
            String code = String.valueOf(res.get("code"));
            if(code != null && code.equals("200")){
                return res.get("data");
            }else{
                throw new ServiceException("获取用户信息失败");
            }
        }else{
            throw new ServiceException("获取用户信息失败");
        }
    }

    /**
     * 用户修改邮箱和昵称
     *
     * @param editMobileUserRequest
     * @param token
     * @return
     */
    @Override
    public String editMobileUser(H5EditMobileUserRequest editMobileUserRequest,String token) {
        String body = openService.editMobileUser(editMobileUserRequest.getUserId(),
                editMobileUserRequest.getEmail(),
                editMobileUserRequest.getNickName(),
                token);
        if (StringUtils.isNotEmpty(body)) {
            JSONObject result = JSON.parseObject(body);
            String code = result.getString("code");
            if (code.equals("200")) {
                //成功
                return "1";
            } else {
                //失败
                String msg = result.getString("msg");
                if (StringUtils.isNotEmpty(msg)) {
                    throw new ServiceException(result.getString("msg"));
                } else {
                    throw new ServiceException("修改失败");
                }
            }
        } else {
            throw new ServiceException("修改失败");
        }
    }

    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public Map<String, Object> getTokenByYkbCode(String token) {
        GatewayResponse res = openService.getUserInfo(token);
        String data = res.getData();
        log.info("getTokenByYkbCode --->{}", JSONUtil.toJsonStr(res));
       if(StringUtils.isNotEmpty(data)){
           cn.hutool.json.JSON arr = JSONUtil.parse(data);
           /**
            * id	用户ID	字符串
            * userName	用户名	字符串
            * userType	用户类型 1：个人用户 3：法人	整数
            * phone	电话号码	字符串
            * certCode	身份证号（该字段可能为空，请从verification对象取用户证件号）	字符串
            * status	用户状态	整数
            * verification	认证信息	Verification对象
            * userVerification	用户认证信息	Verification对象
            * userExtend	用户扩展信息	UserExtend对象
            * org	法人信息，仅账号为法人时返回	org对象
            */
           String ykbUid = null;
           User user = null;
           if (arr.getByPath("id") != null) {
               user = userMapper.selectOne(new LambdaQueryWrapper<User>().eq(User::getYkbUserId, arr.getByPath("id",String.class)));
           }
           if (user == null && arr.getByPath("verification.certCode",String.class)!=null) {
               user = userMapper.selectOne(new LambdaQueryWrapper<User>().eq(User::getYkbUserId, arr.getByPath("verification.userPersonId",String.class)));
               if (user!=null){
                   user.setYkbUserId(arr.getByPath("id",String.class));
                   userMapper.updateById(user);
               }
           }
           if (user!=null){
               LoginUser userInfo = new LoginUser();
               userInfo.setSysUser(new SysUser());
               userInfo.getSysUser().setUserId(user.getUserId());
               userInfo.getSysUser().setUserName(user.getUserName());
               return tokenService.createToken(userInfo);
           }
           if (user==null){
               throw new ServiceException("用户不存在,请先注册",100025);
           }


       }
       return null;
    }
    @Override
    public cn.hutool.json.JSON getYkbJsToken() {
        GatewayResponse res = openService.getJSAPIToken();
        String data = res.getData();
//        log.info("getYkbJsToken --->{}", JSONUtil.toJsonStr(res));
       if(StringUtils.isNotEmpty(data)){
           return JSONUtil.parse(data);
       }
       return null;
    }

    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public Map<String, Object> register(H5RegUserReq h5RegUserReq) {
        LambdaQueryWrapper<BaseStudent> eq = new LambdaQueryWrapper<>();
        eq.eq(BaseStudent::getStuCard, h5RegUserReq.getStuCard());
        BaseStudent find = baseStudentMapper.selectOne(eq);
        if (find!=null){
            throw new ServiceException("该身份证号已注册");
        }
        BaseStudent insertstu = new BaseStudent();
        insertstu.setStuCard(h5RegUserReq.getStuCard());
        insertstu.setStuName(h5RegUserReq.getStuName());
        insertstu.setStuSex(h5RegUserReq.getStuSex());
        insertstu.setStuPolitical(h5RegUserReq.getStuPolitical());
        insertstu.setPhone(h5RegUserReq.getStuPhone());
//        insertstu.setJmStuPhone("");
        insertstu.setStuAddr(h5RegUserReq.getStuAddr());
        insertstu.setStuHealthState(h5RegUserReq.getStuHealthState());
        insertstu.setStuHisCompany(h5RegUserReq.getStuHisCompany());
        insertstu.setStuHisPost(h5RegUserReq.getStuHisPost());
        insertstu.setStuHisJob(h5RegUserReq.getStuHisJob());
        insertstu.setStuHisTitle(h5RegUserReq.getStuHisTitle());
        insertstu.setStuLiveState(h5RegUserReq.getStuLiveState());
        insertstu.setStuEduLevel(h5RegUserReq.getStuEduLevel());
        insertstu.setStuRetireState(h5RegUserReq.getStuRetireState());
        insertstu.setIsAgree(h5RegUserReq.getIsAgree());
        insertstu.setIsHaveElderCollege(h5RegUserReq.getIsHaveElderCollege());
        insertstu.setIsHaveMedical(h5RegUserReq.getIsHaveMedical());
        insertstu.setIsBuyInsure(h5RegUserReq.getIsBuyInsure());
        insertstu.setIsRed("0");
        insertstu.setMedicalDesc(h5RegUserReq.getMedicalDesc());
        insertstu.setCreateBy("h5");
        insertstu.setCreateTime(LocalDateTime.now());
        insertstu.setStatus("1");
        insertstu.setStuCardType(h5RegUserReq.getStuCardType());
        insertstu.setStuBirth(h5RegUserReq.getStuBirth());

        insertstu.setStuPhone(DataTransfer.maskValue(h5RegUserReq.getStuPhone(), SensitiveType.PHONE));
        insertstu.setTmStuCard(DataTransfer.maskValue(h5RegUserReq.getStuCard(), SensitiveType.PHONE));
        insertstu.setJmStuPhone(SensitiveUtil.encrypt(h5RegUserReq.getStuPhone()));
        insertstu.setId(IdUtils.simpleUUID());
        int stubase = baseStudentMapper.insert(insertstu);

        if (h5RegUserReq.getFamilyList()!=null&&!h5RegUserReq.getFamilyList().isEmpty()){
            h5RegUserReq.getFamilyList().forEach(family -> {
                BaseStudentFamily insertFam = new BaseStudentFamily();
                insertFam.setId(IdUtils.simpleUUID());
                insertFam.setStuId(insertstu.getId());
                insertFam.setMemberRelation(family.getMemberRelation());
                insertFam.setMemberName(family.getMemberName());
                insertFam.setMemberPhone(family.getMemberPhone());
                insertFam.setMemberCompany(family.getMemberCompany());
                insertFam.setCreateBy("h5");
                insertFam.setCreateTime(LocalDateTime.now());
                baseStudentFamilyMapper.insert(insertFam);
            });
        }


        User usernew = new User();
        usernew.setUserId(IdUtil.getSnowflakeNextId());
        usernew.setOpenid("");
        usernew.setTopOrgId(null);
        usernew.setDetailId(insertstu.getId());
        usernew.setUserName(insertstu.getStuName());
        usernew.setNickName(insertstu.getStuName());
        usernew.setUserCard(insertstu.getStuCard());
        usernew.setUserType("03");
        usernew.setEmail("");
        usernew.setPhonenumber(h5RegUserReq.getStuPhone());
        usernew.setPassword("");
        usernew.setLoginDate(LocalDateTime.now());
        usernew.setCreateBy("h5");
        if (!StringUtils.isEmpty(h5RegUserReq.getStuCard())){
            usernew.setPassword(SecurityUtils.encryptPassword(h5RegUserReq.getStuCard().substring(h5RegUserReq.getStuCard().length()-6,h5RegUserReq.getStuCard().length())));
        }
        userMapper.insert(usernew);
        // 获取登录token
        LoginUser userInfo = new LoginUser();
        userInfo.setSysUser(new SysUser());
        userInfo.getSysUser().setUserId(usernew.getUserId());
        userInfo.getSysUser().setUserName(usernew.getUserName());
        return tokenService.createToken(userInfo);

    }

    public static void main(String[] args) {
        System.out.println("123456789".substring(3,9));
    }

}

