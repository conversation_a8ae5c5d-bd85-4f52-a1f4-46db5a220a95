package com.ows.ufa.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@Data
@TableName("base_school")
@Schema(description ="实体")
public class BaseSchool implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "院系基础表-主键(UUID)")
    @TableId
    private String id;

    @Schema(description = "学校名称")
    private String schName;

    @Schema(description = "手机号(脱敏)")
    @TableField("tm_sch_head_phone")
    private String schHeadPhone;

    @Schema(description = "负责人")
    private String schHead;

    @Schema(description = "负责人手机号（明文）")
    @TableField("sch_head_phone")
    private String phone;

    @Schema(description = "加密手机号")
    private String jmSchHeadPhone;

    @Schema(description = "学校位置")
    private String schGps;

    @Schema(description = "学校图标")
    private String logoUrl;

    @Schema(description = "学校图片")
    private String mainUrl;

    @Schema(description = "学校地址")
    private String schAddr;

    @Schema(description = "学校gps地址")
    private String schGpsName;

    @Schema(description = "学校简介")
    private String schRecommendation;

}