package com.ows.ufa.system.domain.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@Data
@Schema(description ="实体")
public class H5BaseSchoolCourseRsp implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "主键(UUID)")
    @TableId
    private String id;

    @Schema(description = "学校名称")
    private String schName;

    @Schema(description = "院系")
    private String deptName;
    //专业

    @Schema(description = "专业")
    private String majorName;
    //课程
    @Schema(description = "课程")
    private String courseName;
    @Schema(description = "班级")
    private String className;
    //学籍
    @Schema(description = "学生状态 1在籍 2休学 3已完成")
    private Integer stuStatus;

}