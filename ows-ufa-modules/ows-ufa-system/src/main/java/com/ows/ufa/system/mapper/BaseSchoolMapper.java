package com.ows.ufa.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ows.ufa.system.domain.request.H5BaseSchoolReq;
import com.ows.ufa.system.domain.response.H5BaseSchoolCourseRsp;
import com.ows.ufa.system.domain.response.H5BaseSchoolRsp;
import com.ows.ufa.system.entity.BaseSchool;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@Mapper
public interface BaseSchoolMapper extends BaseMapper<BaseSchool> {


    @Select("<script>" +
            "select * from base_school where is_publish = 1 and is_enable = 1 " +
            "<if test=\"schName != null and schName != ''\">\n" +
            " AND (sch_name like concat('%', #{schName}, '%') or sch_recommendation like concat('%', #{schName}, '%') or sch_addr like concat('%', #{schName}, '%'))" +
            " </if>  </script>")
    List<BaseSchool> queryAll(H5BaseSchoolReq req);

    @Select("<script>" +
            "select * from base_school bs where bs.id in (\n" +
            "    select school_id from (\n" +
            "                                       select\n" +
            "                                           zpc.is_grade_class,\n" +
            "\tzpc.school_id,\n" +
            "\tzpc.plan_id,\n" +
            "\tzps.stu_id,\n" +
            "\tzps.id,\n" +
            "\tbs.stu_name,\n" +
            "\tbs.stu_sex ,\n" +
            "\tbs.stu_phone,\n" +
            "\tbs.stu_card,\n" +
            "\tbs.stu_card_type,\n" +
            "\tbs.stu_birth,\n" +
            "\tzpc.dept_name,\n" +
            "\tzpc.class_name,\n" +
            "\tzpc.school_system,\n" +
            "\tzpc.school_term,\n" +
            "\tzpc.cr_name,\n" +
            "\tzpc.tch_name,\n" +
            "\tcst.class_time_week,\n" +
            "\tcst.class_time_span,\n" +
            "\tcst.start_time,\n" +
            "\tcst.end_time,\n" +
            "\tzps.create_time,\n" +
            "\tzps.is_transfer,\n" +
            "\tzps.stu_status,\n" +
            "\tzps.sign_source,\n" +
            "\tzpc.plan_id,\n" +
            "\tzps.class_id,\n" +
            "\tzps.change_type,\n" +
            "\tzpc.major_name,\n" +
            "\tzpc.course_name,\n" +
            "\tzps.is_pay,\n" +
            "\tzpc.course_fees\n" +
            "from\n" +
            "\tzs_plan_student zps\n" +
            "left join base_student bs on\n" +
            "\tbs.id = zps.stu_id\n" +
            "left join zs_plan_class zpc on\n" +
            "\tzpc.id = zps.class_id\n" +
            "left join base_school_dept bsd on\n" +
            "\tbsd.id = zpc.dept_id\n" +
            "left join class_school_time cst on\n" +
            "\tcst.class_id = zpc.id\n" +
            "where\n" +
            "\t(zps.is_pay = '1'\n" +
            "\t\tor zps.change_type = '2')\n" +
            "\tand zps.is_flag = '0'\n" +
            "\tand zps.stu_id = #{studId}\n" +
            "order by\n" +
            "\tzps.create_time desc,\n" +
            "\tzps.id\n" +
            "                                       )\n" +
            "    group by school_id\n" +
            ")" +
            "<if test=\"req.schName != null and req.schName != ''\">\n" +
            " AND (sch_name like concat('%', #{req.schName}, '%') or sch_recommendation like concat('%', #{req.schName}, '%') or sch_addr like concat('%', #{req.schName}, '%')) " +
            " </if>" +
            "</script>")
    List<BaseSchool> queryMySchool(@Param("studId") String studId, @Param("req") H5BaseSchoolReq h5BaseSchoolReq);
@Select("<script>" +
        "select bs.*,ct.class_num from base_school bs left join (\n" +
        "    select school_id,count(1) class_num from (\n" +
        "                                       select\n" +
        "                                           zpc.is_grade_class,\n" +
        "\tzpc.school_id,\n" +
        "\tzpc.plan_id,\n" +
        "\tzps.stu_id,\n" +
        "\tzps.id,\n" +
        "\tbs.stu_name,\n" +
        "\tbs.stu_sex ,\n" +
        "\tbs.stu_phone,\n" +
        "\tbs.stu_card,\n" +
        "\tbs.stu_card_type,\n" +
        "\tbs.stu_birth,\n" +
        "\tzpc.dept_name,\n" +
        "\tzpc.class_name,\n" +
        "\tzpc.school_system,\n" +
        "\tzpc.school_term,\n" +
        "\tzpc.cr_name,\n" +
        "\tzpc.tch_name,\n" +
        "\tcst.class_time_week,\n" +
        "\tcst.class_time_span,\n" +
        "\tcst.start_time,\n" +
        "\tcst.end_time,\n" +
        "\tzps.create_time,\n" +
        "\tzps.is_transfer,\n" +
        "\tzps.stu_status,\n" +
        "\tzps.sign_source,\n" +
        "\tzpc.plan_id,\n" +
        "\tzps.class_id,\n" +
        "\tzps.change_type,\n" +
        "\tzpc.major_name,\n" +
        "\tzpc.course_name,\n" +
        "\tzps.is_pay,\n" +
        "\tzpc.course_fees\n" +
        "from\n" +
        "\tzs_plan_student zps\n" +
        "left join base_student bs on\n" +
        "\tbs.id = zps.stu_id\n" +
        "left join zs_plan_class zpc on\n" +
        "\tzpc.id = zps.class_id\n" +
        "left join base_school_dept bsd on\n" +
        "\tbsd.id = zpc.dept_id\n" +
        "left join class_school_time cst on\n" +
        "\tcst.class_id = zpc.id\n" +
        "where\n" +
        "\t(zps.is_pay = '1'\n" +
        "\t\tor zps.change_type = '2')\n" +
        "\tand zps.is_flag = '0'\n" +
        "\tand zps.stu_id = #{studId}\n" +
        "order by\n" +
        "\tzps.create_time desc,\n" +
        "\tzps.id\n" +
        "                                       )\n" +
        "    group by school_id\n" +
        ") ct  on bs.id=ct.school_id  where ct.class_num is not null" +
        "<if test=\"req.schName != null and req.schName != ''\">\n" +
        " AND (bs.sch_name like concat('%', #{req.schName}, '%') or bs.sch_recommendation like concat('%', #{req.schName}, '%') or bs.sch_addr like concat('%', #{req.schName}, '%')) " +
        " </if> " +
        "</script>")
    List<H5BaseSchoolRsp> queryMyStuDoc(@Param("studId")String userid,@Param("req")  H5BaseSchoolReq h5BaseSchoolReq);


    @Select("<script>"+"select zpc.is_grade_class, zpc.school_id, zpc.plan_id, zps.stu_id, bsh.sch_name, zps.id, bs.stu_name, bs.stu_sex , bs.stu_phone, bs.stu_card, bs.stu_card_type, bs.stu_birth, zpc.dept_name, zpc.class_name, zpc.school_system, zpc.school_term, zpc.cr_name, zpc.tch_name, cst.class_time_week, cst.class_time_span, cst.start_time, cst.end_time, zps.create_time, zps.is_transfer, zps.stu_status, zps.sign_source, zpc.plan_id, zps.class_id, zps.change_type, zpc.major_name, zpc.course_name, zps.is_pay, zpc.course_fees from zs_plan_student zps left join base_student bs on bs.id = zps.stu_id left join zs_plan_class zpc on zpc.id = zps.class_id left join base_school_dept bsd on bsd.id = zpc.dept_id left join class_school_time cst on cst.class_id = zpc.id left join base_school bsh on zpc.school_id=bsh.id where (zps.is_pay = '1' or zps.change_type = '2') and zps.is_flag = '0' and zps.stu_id = #{studId} <if test=\"req.schName != null and req.schName != ''\"> and (bsh.sch_name like concat('%', #{req.schName}, '%')  or course_name like concat('%', #{req.schName}, '%')  or major_name like concat('%', #{req.schName}, '%') ) </if> <if test=\"req.schoolId != null and req.schoolId != ''\"> AND bsh.id=#{req.schoolId} </if>  order by zps.create_time desc, zps.id " +"</script>")
    List<H5BaseSchoolCourseRsp> queryMyStuClassList(@Param("studId")String userid,@Param("req")  H5BaseSchoolReq h5BaseSchoolReq);
}