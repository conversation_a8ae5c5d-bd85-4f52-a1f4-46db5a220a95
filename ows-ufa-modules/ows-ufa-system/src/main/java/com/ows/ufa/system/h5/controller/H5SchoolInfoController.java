package com.ows.ufa.system.h5.controller;

import com.ows.ufa.common.core.web.controller.BaseController;
import com.ows.ufa.common.core.web.domain.AjaxResult;
import com.ows.ufa.system.config.DataSource;
import com.ows.ufa.system.domain.request.H5BaseSchoolReq;
import com.ows.ufa.system.domain.response.H5BaseSchoolCourseRsp;
import com.ows.ufa.system.domain.response.H5BaseSchoolRsp;
import com.ows.ufa.system.entity.BaseSchool;
import com.ows.ufa.system.enums.DataSourceType;
import com.ows.ufa.system.form.ClubInfoForm;
import com.ows.ufa.system.mapper.BaseSchoolMapper;
import com.ows.ufa.system.request.ClubInfoRequest;
import com.ows.ufa.system.request.IdRequest;
import com.ows.ufa.system.service.ClubInfoService;
import com.ows.ufa.system.service.H5SchoolInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 *  移动端学校相关
 * </p>
 *
 * <AUTHOR>
 * @since 2025年8月2日16:24:48
 */
@RestController
@RequestMapping(value = "h5/schoolinfo")
@Tag(name = "h5Schoolinfo", description = "h5大学信息相关接口")
public class H5SchoolInfoController extends BaseController {

    @Autowired
    private H5SchoolInfoService h5SchoolInfoService;
    @Autowired
    BaseSchoolMapper baseSchoolMapper;

    @RequestMapping(value = "queryAll",method = {RequestMethod.POST,RequestMethod.GET})
    @Operation(summary = "查询老年大学列表")
    public AjaxResult queryAll(@RequestBody H5BaseSchoolReq h5BaseSchoolReq) {
        return success(h5SchoolInfoService.queryAll(h5BaseSchoolReq));
    }

    @RequestMapping(value = "queryMySchool",method = {RequestMethod.POST,RequestMethod.GET})
    @Operation(summary = "查询我的大学列表")
    public AjaxResult queryMySchool(@RequestBody H5BaseSchoolReq h5BaseSchoolReq) {
        return success(h5SchoolInfoService.queryMySchool(h5BaseSchoolReq));
    }
    @RequestMapping(value = "queryMyStuDoc",method = {RequestMethod.POST,RequestMethod.GET})
    @Operation(summary = "查询我的学习档案")
    public AjaxResult queryMyStuDoc(@RequestBody H5BaseSchoolReq h5BaseSchoolReq) {
        return success(h5SchoolInfoService.queryMyStuDoc(h5BaseSchoolReq));
    }
    @RequestMapping(value = "queryMyStuClassList",method = {RequestMethod.POST,RequestMethod.GET})
    @Operation(summary = "查询我的学习档案-课程列表")
    public AjaxResult queryMyStuClassList(@RequestBody H5BaseSchoolReq h5BaseSchoolReq) {
        return success(h5SchoolInfoService.queryMyStuClassList(h5BaseSchoolReq));
    }


    @RequestMapping(value = "test",method = {RequestMethod.POST,RequestMethod.GET})
    @Operation(summary = "测试")
    @Profile("test")
    @DataSource(DataSourceType.SLAVE)
    public AjaxResult test() {
        H5BaseSchoolReq req = new H5BaseSchoolReq();
        req.setSchName("");
        req.setSchoolId("1");
        List ret = baseSchoolMapper.queryMyStuClassList("25fbfdd516c24753b5f39f28b434c596", req);
//        List ret = baseSchoolMapper.queryMyStuDoc("25fbfdd516c24753b5f39f28b434c596", req);
        return success(ret);
    }

}
